{"/api/wp-proxy/cache/invalidations/route": "app/api/wp-proxy/cache/invalidations/route.js", "/api/wp-proxy/posts/route": "app/api/wp-proxy/posts/route.js", "/api/wp-proxy/categories/route": "app/api/wp-proxy/categories/route.js", "/api/wp-proxy/auth/status/route": "app/api/wp-proxy/auth/status/route.js", "/api/wp-proxy/posts/batch-upvotes/route": "app/api/wp-proxy/posts/batch-upvotes/route.js", "/api/wp-proxy/iq-score/[userId]/route": "app/api/wp-proxy/iq-score/[userId]/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/wp-proxy/members/custom/route": "app/api/wp-proxy/members/custom/route.js", "/api/wp-proxy/connections/route": "app/api/wp-proxy/connections/route.js", "/api/wp-proxy/notifications/route": "app/api/wp-proxy/notifications/route.js", "/api/wp-proxy/connections/status/[otherUserId]/route": "app/api/wp-proxy/connections/status/[otherUserId]/route.js", "/api/wp-proxy/messages/unread-count/route": "app/api/wp-proxy/messages/unread-count/route.js", "/api/wp-proxy/user/assigned-vendors/route": "app/api/wp-proxy/user/assigned-vendors/route.js", "/page": "app/page.js", "/login/page": "app/login/page.js"}