'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Search, User, Settings, LogOut, HelpCircle, MessageSquare } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { NotificationMenu } from '@/components/ui/NotificationMenu';
import { useNotifications } from '@/contexts/NotificationContext';
import { useMessaging } from '@/components/messaging';
import { Leaderboard } from '@/components/leaderboard';
import { UserAvatarWithRank } from '@/components/ui/UserAvatarWithRank';
import Image from 'next/image';
import { MobileMenu } from './MobileMenu';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SupportModal } from '@/components/ui/SupportModal';
import { FeedbackModal } from '@/components/ui/FeedbackModal';
import { SearchModal } from '@/components/ui/SearchModal';
import { BackToTopButton } from '@/components/ui/BackToTopButton';

export function Header() {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const { unreadCount } = useNotifications();
  const { openMessageList, hasUnreadMessages, totalUnreadCount } = useMessaging();

  // State for notification menu
  const [notificationMenuOpen, setNotificationMenuOpen] = useState(false);

  // State for leaderboard
  const [leaderboardOpen, setLeaderboardOpen] = useState(false);

  // State for support and feedback modals
  const [supportModalOpen, setSupportModalOpen] = useState(false);
  const [feedbackModalOpen, setFeedbackModalOpen] = useState(false);

  // State for search modal
  const [searchModalOpen, setSearchModalOpen] = useState(false);

  // Track hydration state to prevent mismatch
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Global keyboard shortcut for search (Cmd+K / Ctrl+K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setSearchModalOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <>
      <header className="border-b bg-white sticky top-0 z-50 h-[80px] md:h-[90px]">
        <div className="w-full h-full flex items-center justify-center px-3 md:px-4 relative">
          <div className="w-full max-w-[1360px] flex items-center justify-between">
            {/* Left side - Logo aligned with sidebar */}
            <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-8">
              <MobileMenu />
              <Link
                href="/"
                className="flex items-center h-[36px] md:h-[42px] object-contain"
                onClick={() => {
                  // Clear any active category filters when logo is clicked
                  if ((window as any).setFeedCategory) {
                    (window as any).setFeedCategory(null);
                  }
                }}
              >
                {/* Mobile - Small icon */}
                <Image
                  src="/images/icons/icon-tourism.svg"
                  alt="TourismIQ"
                  height={28}
                  width={28}
                  className="h-7 w-7 md:hidden object-contain"
                />
                {/* Desktop - Full logo */}
                <Image
                  src="/images/logo.svg"
                  alt="TourismIQ"
                  height={42}
                  width={168}
                  className="hidden md:block h-[42px] max-w-[168px] object-contain"
                />
              </Link>

              {/* Mobile Search Icon - only show on mobile and when authenticated */}
              {isHydrated && !isLoading && isAuthenticated && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="min-[1024px]:hidden text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff]/20 h-8 w-8 sm:h-9 sm:w-9"
                  onClick={() => setSearchModalOpen(true)}
                  title="Search"
                >
                  <Search className="size-[1.4rem] sm:h-[18px] sm:w-[18px]" />
                </Button>
              )}
            </div>

            {/* Spacer for layout balance */}
            <div className="flex-1"></div>

            {/* Right side - User actions aligned with content area */}
            {isHydrated && !isLoading && (
              <div className="flex items-center ml-auto space-x-2 md:space-x-4 pr-2 md:pr-4">
                {isAuthenticated ? (
                  <>
                    {/* Messages icon */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="relative text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10"
                      onClick={openMessageList}
                      title="Messages"
                    >
                      <Image
                        src="/images/icons/icon-messages.svg"
                        alt="Messages"
                        width={18}
                        height={18}
                        className="h-[18px] w-[18px] md:h-5 md:w-5"
                      />
                      {hasUnreadMessages && (
                        <span className="absolute -top-0.5 -right-0.5 md:-top-1 md:-right-1 flex h-4 w-4 md:h-5 md:w-5 items-center justify-center rounded-full bg-red-500 text-[9px] md:text-[10px] text-white font-bold">
                          {totalUnreadCount > 9 ? '9+' : totalUnreadCount}
                        </span>
                      )}
                    </Button>

                    {/* Notification icon to trigger notification menu */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="relative text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10"
                      onClick={() => setNotificationMenuOpen(true)}
                    >
                      <Image
                        src="/images/icons/icon-notification.svg"
                        alt="Notifications"
                        width={18}
                        height={18}
                        className="h-[18px] w-[18px] md:h-5 md:w-5"
                      />
                      {unreadCount > 0 && (
                        <span className="absolute -top-0.5 -right-0.5 md:-top-1 md:-right-1 flex h-4 w-4 md:h-5 md:w-5 items-center justify-center rounded-full bg-red-500 text-[9px] md:text-[10px] text-white font-bold">
                          {unreadCount > 9 ? '9+' : unreadCount}
                        </span>
                      )}
                    </Button>

                    {/* Notification Menu */}
                    <NotificationMenu
                      isOpen={notificationMenuOpen}
                      onClose={() => setNotificationMenuOpen(false)}
                      onNotificationsRead={() => {}}
                    />

                    {/* Leaderboard icon - hidden on small screens */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-[#3d405b] hover:text-[#3d405b] hover:bg-[#5cc8ff] h-9 w-9 md:h-10 md:w-10 hidden sm:flex"
                      onClick={() => setLeaderboardOpen(true)}
                      title="View Leaderboard"
                    >
                      <Image
                        src="/images/icons/icon-leaderboard.svg"
                        alt="Leaderboard"
                        width={18}
                        height={18}
                        className="h-[18px] w-[18px] md:h-5 md:w-5"
                      />
                    </Button>

                    {/* Leaderboard Menu */}
                    <Leaderboard
                      isOpen={leaderboardOpen}
                      onClose={() => setLeaderboardOpen(false)}
                    />

                    <DropdownMenu modal={false}>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          className="relative h-10 w-10 md:h-12 md:w-12 rounded-full p-0 hover:bg-[#5cc8ff]/10 cursor-pointer"
                          asChild
                        >
                          <div>
                            <UserAvatarWithRank
                              userId={user?.id || 0}
                              avatarUrl={
                                user?.avatarUrl ||
                                (typeof user?.acf?.profile_picture === 'string'
                                  ? user.acf.profile_picture
                                  : user?.acf?.profile_picture?.url) ||
                                (user?.acf?.avatar_url as string) ||
                                '/images/avatar-placeholder.svg'
                              }
                              displayName={user?.name || 'User'}
                              size="h-7 w-7 md:h-8 md:w-8"
                              containerSize="w-10 h-10 md:w-12 md:h-12"
                              showRankBadge={true}
                              userRoles={user?.roles}
                            />
                          </div>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-56 rounded-[30px] shadow-[0_20px_60px_-12px_rgba(0,0,0,0.25),0_8px_24px_-4px_rgba(0,0,0,0.15)] p-4"
                        align="end"
                        forceMount
                      >
                        <DropdownMenuLabel className="font-normal">
                          <div className="flex flex-col space-y-1">
                            <p className="text-sm font-medium leading-none">
                              {user?.name || 'User'}
                            </p>
                            <p className="text-xs leading-none text-muted-foreground">
                              {user?.email || ''}
                            </p>
                          </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          asChild
                          className="flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)] cursor-pointer"
                        >
                          <Link
                            href={user && user.username ? `/profile/${user.username}` : '/profile'}
                            className="w-full flex items-center"
                          >
                            <User className="mr-2 h-4 w-4" />
                            <span>Profile</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          asChild
                          className="flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)] cursor-pointer"
                        >
                          <Link href="/settings" className="w-full flex items-center">
                            <Settings className="mr-2 h-4 w-4" />
                            <span>Settings</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="cursor-pointer flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]"
                          onClick={() => setSupportModalOpen(true)}
                        >
                          <HelpCircle className="mr-2 h-4 w-4" />
                          <span>Support</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="cursor-pointer flex items-center rounded-full px-4 py-2 text-gray-600 font-medium transition-all duration-200 ease-in-out hover:bg-[rgba(92,200,255,0.15)] focus:bg-[rgba(92,200,255,0.15)]"
                          onClick={() => setFeedbackModalOpen(true)}
                        >
                          <MessageSquare className="mr-2 h-4 w-4" />
                          <span>Feedback</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="cursor-pointer flex items-center rounded-full px-4 py-2 text-red-600 font-medium transition-all duration-200 ease-in-out hover:bg-red-50 focus:bg-red-50 focus:text-red-700"
                          onClick={handleLogout}
                        >
                          <LogOut className="mr-2 h-4 w-4" />
                          <span>Log out</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      asChild
                      className="text-[#3d405b] border-2 border-[#5cc8ff] hover:bg-[#5cc8ff]/25 hover:text-[#3d405b] font-extrabold h-8 px-3 text-sm md:h-10 md:px-4 md:text-base"
                    >
                      <Link href="/login">Log In</Link>
                    </Button>
                    <Button
                      asChild
                      className="bg-[#5cc8ff] text-[#3d405b] hover:bg-[#5cc8ff]/80 font-extrabold border-2 border-[#5cc8ff] h-8 px-3 text-sm md:h-10 md:px-4 md:text-base"
                    >
                      <Link href="/register">Sign Up</Link>
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>

          {/* Absolutely centered search bar */}
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-[350px] lg:max-w-[400px] px-4 hidden min-[1024px]:block">
            <button
              onClick={() => setSearchModalOpen(true)}
              className="relative w-full h-10 lg:h-12 bg-gray-100 border border-gray-200 rounded-full hover:bg-gray-50 transition-colors group"
            >
              <div className="flex items-center h-full">
                <Search className="absolute left-3 h-4 w-4 lg:h-5 lg:w-5 text-gray-400 group-hover:text-gray-600" />
                <span className="pl-9 lg:pl-10 pr-10 lg:pr-12 text-sm lg:text-base text-gray-500 group-hover:text-gray-700">
                  Search posts, people, vendors...
                </span>
                <div className="absolute right-3 text-xs text-muted-foreground">⌘K</div>
              </div>
            </button>
          </div>

          {/* Back to Top Button - Absolutely positioned */}
          <BackToTopButton />
        </div>
      </header>

      {/* Support Modal */}
      <SupportModal isOpen={supportModalOpen} onClose={() => setSupportModalOpen(false)} />

      {/* Feedback Modal */}
      <FeedbackModal isOpen={feedbackModalOpen} onClose={() => setFeedbackModalOpen(false)} />

      {/* Search Modal */}
      <SearchModal isOpen={searchModalOpen} onClose={() => setSearchModalOpen(false)} />
    </>
  );
}
